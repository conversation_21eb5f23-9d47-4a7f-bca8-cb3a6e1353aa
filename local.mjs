import concurrently from 'concurrently';
import { existsSync, readFileSync } from 'fs';

const localWatchlist = (() => {
  /*
  This is an object used to determine if a service should be run with the watch flag.
  Create a file named `local.watchlist.json` in the root directory. An example of the file is shown below:

  {
    "auth": true,
    "business": false,
    "booking": false
  }

  Services not found in the json, and services set to true both result in the service being run with the watch flag.
  */

  const watchlistFileName = 'local.watchlist.json';
  if (existsSync(watchlistFileName)) {
    return JSON.parse(readFileSync(watchlistFileName));
  }
  return {};
})();

const services = {
  auth: {
    name: 'Auth',
    prefixColor: '#ff0000',
    priority: 1,
  },
  business: {
    name: 'Business',
    prefixColor: '#ff8000',
    priority: 1,
  },
  booking: {
    name: 'Booking',
    prefixColor: '#ff8080',
    priority: 3,
  },
  cron: {
    name: 'CRON',
    prefixColor: '#ffff00',
    priority: 3,
  },
  gateway: {
    name: 'Gateway',
    prefixColor: '#80ff00',
    priority: 1,
  },
  giftcards: {
    name: 'Gift Cards',
    prefixColor: '#00ff00',
    priority: 3,
  },
  inventory: {
    name: 'Inventory',
    prefixColor: '#00ff80',
    priority: 3,
  },
  kds: {
    name: 'KDS',
    prefixColor: '#00ffff',
    priority: 3,
  },
  menu: {
    name: 'Menu',
    prefixColor: '#0080ff',
    priority: 1,
  },
  ordering: {
    name: 'Ordering',
    prefixColor: '#0000ff',
    priority: 1,
  },
  printing: {
    name: 'Printing',
    prefixColor: '#8000ff',
    priority: 2,
  },
  realtime: {
    name: 'Realtime',
    prefixColor: '#ff0080',
    priority: 2,
  },
  reporting: {
    name: 'Reporting',
    prefixColor: '#7bb3ee',
    priority: 1,
  },
  scheduling: {
    name: 'Scheduling',
    prefixColor: '#fbaf71',
    priority: 2,
  },
  staffing: {
    name: 'Staffing',
    prefixColor: '#745399',
    priority: 1,
  },
  uploader: {
    name: 'Uploader',
    prefixColor: '#91abd0',
    priority: 3,
  },
  ai: {
    name: 'AI',
    prefix: '#210393',
    priority: 3,
  },
  banking: {
    name: 'Banking',
    prefix: '#ED8A80',
    priority: 2,
  },
};

const highPriorityServiceCount = Object.keys(services).filter((service) => services[service].priority === 1).length;

const mediumPriorityServiceCount = Object.keys(services).filter((service) => services[service].priority === 2).length;

const lowPriorityServiceCount = Object.keys(services).filter((service) => services[service].priority === 3).length;

const getDelay = (service) => {
  if (service.priority === 1) {
    return 0;
  }
  if (service.priority === 2) {
    return highPriorityServiceCount * 2 + 5;
  }
  if (service.priority === 3) {
    return highPriorityServiceCount * 2 + mediumPriorityServiceCount * 2 + 5;
  }
};

const { result } = concurrently(
  [
    {
      command: `yarn start:db`,
      name: `Database Service`,
      prefix: '#0abc12',
    },
    ...Object.keys(services)
      .sort((a, b) => a.priority - b.priority)
      .filter((service) => localWatchlist[service] !== 'off')
      .map((service, i) => ({
        command: `sleep ${i * 2 + getDelay(services[service])} && yarn ${
          (localWatchlist[service] !== undefined ? localWatchlist[service] : true) ? 'start:dev' : 'start'
        } ${service}`,
        name: `${services[service].name} Service`,
        prefixColor: services[service].prefixColor,
        env: {
          MICROSERVICE_CLIENT_HOST: '127.0.0.1',
          MICROSERVICE_TRANSPORTER_URL_HOST: '127.0.0.1',
          POSTGRES_PORT: 5433,
          POSTGRES_HOST: '127.0.0.1',
        },
      })),
  ],
  {
    prefix: 'name',
    killOthers: ['failure', 'success'],
    restartTries: 3,
  },
);
result.then(() => {
  console.log('Service terminated');
});
