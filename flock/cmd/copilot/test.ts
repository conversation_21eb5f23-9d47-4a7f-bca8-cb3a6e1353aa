import { Command } from 'commander';
import { getSource } from '~/readsource';
import { SUB_PRODUCTS_CTE_QUERY, SUB_TABS_CTE_QUERY, SUB_WORK_EVENTS_CTE_QUERY } from '../../../apps/reporting/src/services/queries/base-queries';
import Agent from '../../../apps/reporting/src/services/agents/agent';
import SchemaSelectorAgent from '../../../apps/reporting/src/services/agents/schemaSelectorAgent';
import QueryDecomposerAgent from '../../../apps/reporting/src/services/agents/queryDecomposerAgent';
import SQLGeneratorAgent from '../../../apps/reporting/src/services/agents/sqlGeneratorAgent';
import ValidatorAgent from '../../../apps/reporting/src/services/agents/validatorAgent';
import ConfidenceScorerAgent from '../../../apps/reporting/src/services/agents/confidenceScoreAgent';
import SupervisorAgent from '../../../apps/reporting/src/services/agents/supervisorAgent';

async function executeSQLQuery(query: string) {
  const source = getSource();

  // Check if already initialized, if not, initialize it
  if (!source.isInitialized) {
    await source.initialize();
  }

  const queryRunner = source.createQueryRunner();

  if (queryRunner) {
    await queryRunner.connect();
    const response = await queryRunner.query(query);
    queryRunner.release();
    return response;
  } else {
    throw new Error('Failed to create query runner');
  }
}

class RestaurantSQLGenerator {
  agents: { [key: string]: Agent };

  constructor() {
    this.agents = {
      schemaSelector: new SchemaSelectorAgent(),
      queryDecomposer: new QueryDecomposerAgent(),
      sqlGenerator: new SQLGeneratorAgent(),
      validator: new ValidatorAgent(),
      confidenceScorer: new ConfidenceScorerAgent(),
      supervisor: new SupervisorAgent(),
    };
  }

  async generateSQL(userQuery: string, businessId: string, options: { [T: string]: any } = {}) {
    const context: { [T: string]: any } = {
      businessId,
      userQuery,
      timestamp: new Date().toISOString(),
      options,
    };

    try {
      // Step 1: Schema Selection and Entity Matching
      console.log('🔍 Analyzing query and selecting schema...');
      const schemaInfo = await this.agents.schemaSelector.execute(userQuery, businessId);
      console.log({ schemaInfo });
      context.schemaInfo = schemaInfo;

      // Step 2: Query Decomposition
      console.log('🔨 Decomposing query into steps...');
      const decomposition = await this.agents.queryDecomposer.execute(userQuery, {
        schemaInfo,
        businessId,
      });
      console.log({ decomposition: JSON.stringify(decomposition, null, 2) });
      context.decomposition = decomposition;

      // Step 3: SQL Generation
      console.log('⚡ Generating SQL query...');
      const sqlQuery = await this.agents.sqlGenerator.execute(userQuery, {
        steps: decomposition.steps,
        schemaInfo,
        entityMatches: schemaInfo.products_matches || {},
        timeContext: schemaInfo.time_context,
        businessId,
      });
      const cteHeaders = ((context.schemaInfo?.ctes || []) as string[])
        .map((cte) => {
          switch (cte) {
            case 'products':
              return SUB_PRODUCTS_CTE_QUERY;
            case 'tabs':
              return SUB_TABS_CTE_QUERY;
            case 'work_events':
              return SUB_WORK_EVENTS_CTE_QUERY;
            default:
              return null;
          }
        })
        .filter(Boolean);

      let finalQuery;
      if (cteHeaders.length > 0) {
        // Ensure sqlQuery doesn't start with WITH
        const cleanSqlQuery = sqlQuery.replace(/^\s*WITH\s+/i, '').trim();
        finalQuery = `WITH ${cteHeaders.join(',\n')}\n\n${cleanSqlQuery}`;
      } else {
        finalQuery = sqlQuery;
      }

      context.sqlQuery = finalQuery;
      // const cteHeadersString = cteHeaders.length > 0 ? `WITH ${cteHeaders.join(', \n')}\n` : '';
      // console.log({ sqlQuery: JSON.stringify(sqlQuery, null, 2) });
      // context.sqlQuery = cteHeadersString + sqlQuery;

      // Step 4: Validation
      console.log('✅ Validating SQL query...');
      const validation = await this.agents.validator.execute(context.sqlQuery, {
        originalQuery: userQuery,
        schemaInfo,
      });
      console.log(JSON.stringify(validation));
      context.validation = validation;
      if (context.suggested_query) {
        context.previousSqlQuery = context.sqlQuery;
        context.sqlQuery = context.suggested_query;
      }

      // Step 5: Confidence Scoring
      console.log('📊 Scoring confidence...');
      const confidence = await this.agents.confidenceScorer.execute(userQuery, {
        sqlQuery: context.sqlQuery,
        schemaInfo,
        validation,
        decomposition,
      });
      console.log({ confidence });
      context.confidence = confidence;

      // Step 6: Supervisor Decision
      console.log('🎯 Making final decision...');
      const decision = await this.agents.supervisor.execute(userQuery, {
        ...context,
        allAgentOutputs: {
          schemaInfo,
          decomposition,
          sqlQuery: context.sqlQuery,
          validation,
          confidence,
        },
      });

      console.log({ decision });

      const shouldExecute = validation.valid && (confidence.overall_confidence > 70 || validation.suggested_query);

      // Return comprehensive result
      return {
        success: shouldExecute,
        query: context.sqlQuery,
        confidence: confidence.overall_confidence,
        recommendation: decision.recommendation || confidence.recommendation,
        usedSuggestedQuery: !!validation.suggested_query,
        metadata: {
          queryType: schemaInfo.query_type,
          complexity: decomposition.complexity,
          entities: schemaInfo.entities_to_search,
          validation,
          decision,
        },
        alternatives: decision.alternative_queries || [],
        debugInfo: options.debug ? context : undefined,
      };
    } catch (error) {
      console.error('❌ SQL generation failed:', error);
      return {
        success: false,
        error: (error as Error).message,
        recommendation: 'fallback',
      };
    }
  }

  async executeQuery(userQuery: string, businessId: string, options = {}) {
    const result = await this.generateSQL(userQuery, businessId, options);

    if (result.success && result.recommendation === 'execute') {
      try {
        console.log('🚀 Executing SQL query...');
        const data = await executeSQLQuery(result.query);
        return {
          ...result,
          data,
          rowCount: data.length,
        };
      } catch (error) {
        console.error('❌ Query execution failed:', error);
        return {
          ...result,
          success: false,
          executionError: (error as Error).message,
        };
      }
    }

    return result;
  }
}

async function handler(businessId: string, question: string) {
  const generator = new RestaurantSQLGenerator();

  const result = await generator.generateSQL(question, businessId, { debug: false });

  console.log({ result });

  console.log('\nResult:');
  console.log('Success:', result.success);
  console.log('Confidence:', result.confidence);
  console.log('Recommendation:', result.recommendation);

  if (result.success) {
    console.log('\nGenerated SQL:');
    console.log(result.query);
  } else {
    console.log('\nError:', result.error);
    console.log('Alternatives:', result.alternatives);
  }
}

export default function register(program: Command) {
  program
    .command('query-copilot')
    .description('Ask a question about your business')
    .argument('<businessId>', 'The business ID to query')
    .argument('<question>', 'The question you have about your business')
    .action(handler);
}
