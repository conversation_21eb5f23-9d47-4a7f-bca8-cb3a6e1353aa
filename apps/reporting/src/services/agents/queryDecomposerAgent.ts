import Agent from './agent';

// Breaks complex queries into steps
class QueryDecomposerAgent extends Agent {
  constructor() {
    super(
      'QueryDecomposer',
      `Your are an expert at decomposing complex restaurant queries into logical steps.

Given a query and schema information, break it down into:
1. Data retrieval steps
2. Aggregation requirements
3. Filtering conditions
4. Join requirements
5. Output format needs

Return JSON with:
{
  "steps": [
  	{
      "steps"; 1,
      "description": "What this step does",
      "type": "retrieve|aggregate|filter|join|format",
	  "details": {}
    },
	"complexity": "simple|medium|complex",
	"requires_multiple_queries": boolean,
	"output_structure": {
	  "format": "single_value|list|aggregated|custom_json",
	  "fields": []
	}
  ]
}`,
    );
  }
}

export default QueryDecomposerAgent;
