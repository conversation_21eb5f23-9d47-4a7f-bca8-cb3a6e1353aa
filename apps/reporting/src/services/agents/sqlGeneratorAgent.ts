import { DAILY_SALES_LABOUR_GUEST_CTE_QUERY, PRODUCTS_CTE_QUERY, SHIFTS_CTE_QUERY, TABS_CTE_QUERY, WORK_EVENTS_CTE_QUERY } from '../queries/base-queries';
import Agent from './agent';

// Creates the actual SQL query
class SQLGeneratorAgent extends Agent {
  constructor() {
    super(
      'SQLGenerator',
      `You are a PostgresSQL expert specializing in restaurant management systems.

CRITICAL: When a CTE is specified, you MUST use it. The CTEs are pre-defined.

NOTE: The CTE will be injected into the SQL query using it's given name. You must NOT define the CTE in your output.

CRITICAL INSTRUCTION: 
- CTEs (Common Table Expressions) are ALREADY PROVIDED
- You must NEVER create WITH clauses, CTEs, or subqueries
- Start your query with SELECT, not WITH
- Use the provided CTE names directly in your FROM clause

Business Logic Rules:
1. For sales amounts, always use sub_total_amount from products CTE
2. Apply business_id filter ALWAYS
3. For staff names, join StaffMember to Users: "StaffMember".user_id = "Users".id
4. Handle voided quantities properly in sales calculations
5. Use ordered_day for date grouping, ordered_at for precise timestamps

Time Context Handling:
- This week: ordered_day >= DATE_TRUNC('week', CURRENT_DATE)
- Last week: ordered_day >= DATE_TRUNC('week', CURRENT_DATE) - INTERVAL '1 week' AND ordered_day < DATE_TRUNC('week', CURRENT_DATE)
- "today" = CURRENT_DATE

Example of CORRECT output:
SELECT 
  ROUND(SUM(CASE WHEN "ordered_day" >= DATE_TRUNC('week', CURRENT_DATE) THEN "sub_total_amount" END)::NUMERIC / 100, 2) as this_week_sales,
  ROUND(SUM(CASE WHEN "ordered_day" >= DATE_TRUNC('week', CURRENT_DATE) - INTERVAL '1 week' 
                 AND "ordered_day" < DATE_TRUNC('week', CURRENT_DATE) THEN "sub_total_amount" END)::NUMERIC / 100, 2) as last_week_sales
FROM products 
WHERE "business_id" = 'your-business-id';

WRONG examples (DO NOT DO THIS):
- WITH anything AS (...)
- this_week AS (...)  
- Creating subqueries with SELECT inside parentheses
- Multiple SELECT statements

PostgreSQL Rules:
1. ALWAYS cast to NUMERIC before ROUND: ROUND(value::NUMERIC, 2)
2. Use quoted identifiers: "ordered_at", "sub_total_amount"  
3. Handle NULL with COALESCE()
4. Apply business_id filter ALWAYS
5. Use proper date functions: DATE_TRUNC('week', CURRENT_DATE)

Critical Rules:
1. ALWAYS use quoted identifiers for mixed-case: "WorkEvents", "ReportingProduct",
2. For staff names, join StaffMember to Users: "StaffMember".user_id = "Users".id
3. Calculate hours from WorkEvents using EXTRACT(EPOCH FROM (end_time - start_time)) / 3600
4. When required, use ILIKE with 4-character keywords for fuzzy matching: name ILIKE %guin%
5. Use ReportingProduct for sales, WorkEvents for labor (not Shifts)
6. Apply business_id and date filters ALWAYS
7. Return ONLY valid PostgresSQL query ending with ;

String Matching
- Extract distinctive 4-char keywords: "Guinness" -> '%guin%'
- Use singular forms: '%bar%' not '%bars%'
- Handle variations: REPLACE(LOWER(name), ',', '')

Input includes:
- Query steps
- Matched entities from RAG
- Available CTEs
- Time context

Return ONLY the SQL query, no explanations.`,
    );
  }

  formatInput(input: string, context: { steps: any[]; schemaInfo: any; entityMatches: any; timeContext: any; businessId: string }) {
    const { steps, schemaInfo, entityMatches, timeContext, businessId } = context;

    // Prepare CTE context
    const availableCTEs = {
      products: PRODUCTS_CTE_QUERY,
      tabs: TABS_CTE_QUERY,
      work_events: WORK_EVENTS_CTE_QUERY,
      schedule: SHIFTS_CTE_QUERY,
      daily_sales_labour_guest: DAILY_SALES_LABOUR_GUEST_CTE_QUERY,
    };

    const requiredCTEs = context?.schemaInfo?.ctes || [];

    return `Query: ${input}
Business ID: ${businessId}
Time Context: ${JSON.stringify(timeContext)}
Entity Matches: ${JSON.stringify(entityMatches)}
Query Steps: ${JSON.stringify(steps)}
Available CTEs: ${Object.keys(availableCTEs)
      .filter((cte) => requiredCTEs.includes(cte))
      .join(', ')}

Here are the columns for each CTE:
- products:
  - id (uuid): The product ID
  - alcohol (boolean): The product is an alcoholic drink
  - menu_category (string): The name of the menu category
  - menu_name (string): The name of the menu
  - order_type (string): This is the order type (dineIn, delivery, carryOut, online)
  - name (string): The name of the product
  - ordered_quantity (number): The quantity of the product ordered
  - type (string): This is the type of the product (Misc., Sake., Liquor., Beer., Food., Wine., Non-Alcoholic.)
  - subType (string): This is the sub type of the product
  - business_id (uuid): This is the ID of the business
  - ordered_at (date): This is when the product was ordered
  - ordered_day (date): This is when the product was ordered
  - sub_total_amount (number): This is amount in cents that this cost
  - voided_quantity (number): This is the number of items this product was voided
  - through_quantity (number): This is the quantity that was ordered
  - discount_amount (json array): This is the discounts applied to the product with the format being { amount: number, discountId: string, discountName: string }[]
- tabs:
    - id (uuid): The tab ID
    - business_id (uuid): This is the ID of the business
    - number_of_guests (number): The number of guests on the tab
    - minutes_open (number): The number of minutes the tab was open
    - created_day (date): The day the tab was created
    - created_at (date): The timestamp the tab was created
- work_events:
    - id (uuid): The work event ID
    - business_id (uuid): This is the ID of the business
    - staff_id (uuid): This is the ID of the staff member
    - role_id (uuid): This is the ID of the role
    - start_time (date): This is the start time of the work event
    - end_time (date): This is the end time of the work event
    - regular_minutes (number): The number of regular minutes worked
    - overtime_minutes (number): The number of overtime minutes worked
    - unpaid_minutes (number): The number of unpaid minutes worked
    - hourly_rate (number): The rate in cents of the hourly rate
    - overtime_rate (number): The rate in cents of the overtime rate
    - minutes_worked (number): The total number of minutes worked
    - work_day (date): The day the work event occurred
    - timezone (date): The timezone of the work event
  NOTE: We only use regular_minutes and overtime_minutes in special cases so to find the amount of time worked ALWAYS subtract end_time by start_time.
- schedule:
    - business_id (uuid): This is the ID of the business
    - department_id (uuid): The is the ID of the department
    - role_id (uuid): This is the ID of the role
    - start_time (date): This is the start time for their shift
    - end_time (date): This is the end time for their shift
    - hourly_rate (number): This is the pay rate in cents per hour
    - work_day (date): This is the date the shift was scheduled for
    - isodow (number): This is the day of the week the shift was scheduled for (1 = Monday, 7 = Sunday)
  NOTE: Several businesses don't use the schedule or shifts so to calculate amount worked historically ALWAYS use work events. Only rely on schedule when you need to know if someone was scheduled for shift.
- daily_sales_labour_guest:
    - ordered_day (date): This is the date the sales and labour occurred
    - business_id (uuid): This is the ID of the business
    - sales_total (number): This is the total sales amount in cents
    - dine_in_total (number): This is the total amount of dine sales in cents
    - carry_out_total (number): This is the total amount of carry out sales in cents
    - delivery_total (number): This is the total amount of delivery sales in cents
    - sales_unit (number): This is the total number of units sold 
    - discount_total (number): This is the total amount of discounts in cents
    - guest_count (number): This is the total number of guests
    - check_count (number): This is the total number of checks served
    - minutes_open (number): This is the total number of minutes the it was open
    - total_pay (number): This is the total amount of labour in cents
    - regular_pay (number): This is the total amount of regular labour in cents
    - overtime_pay (number): This is the total amount of overtime labour in cents
    - minutes_worked (number): The number of total minutes worked
    - regular_minutes (number): This is the total number of regular minutes worked
    - unpaid_minutes (number): This is the total number of unpaid minutes worked
    - overtime_minutes (number): This is the total number of overtime minutes worked

DO NOT create your own CTE definition!
`;
  }

  parseResponse(response: string) {
    // Clean up the SQL query
    let sql = response.trim();

    // Remove markdown code blocks if present
    if (sql.includes('```sql')) {
      const sqlMatch = sql.match(/```sql\s*([\s\S]*?)\s*```/);
      if (sqlMatch) {
        sql = sqlMatch[1].trim();
      }
    }

    // Remove entire WITH blocks
    sql = sql.replace(/WITH\s+[\s\S]*?(?=SELECT)/im, '').trim();

    // Remove any standalone CTE definitions that might be left
    sql = sql.replace(/^\w+\s+AS\s*\([^)]*\)\s*,?\s*/gm, '').trim();

    // Remove any remaining comma-separated CTE patterns
    sql = sql.replace(/,\s*\w+\s+AS\s*\([^)]*\)/gm, '').trim();

    // Ensure we start with SELECT
    if (!sql.match(/^\s*SELECT/i)) {
      throw new Error(`Generated SQL must start with SELECT. Got: ${sql.substring(0, 50)}...`);
    }

    // Fix ROUND function
    sql = sql.replace(/ROUND\(([^,]+),\s*(\d+)\)/g, 'ROUND(($1)::NUMERIC, $2)');

    // Remove trailing semicolon if it's outside the query
    sql = sql.replace(/;$/, '').trim();

    if (!sql.endsWith(';')) {
      return sql + ';';
    }

    return sql;
  }
}

export default SQLGeneratorAgent;
