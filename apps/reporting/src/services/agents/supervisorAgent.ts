import Agent from './agent';

// Orchestrates the workflow
class SupervisorAgent extends Agent {
  constructor() {
    super(
      'Supervisor',
      `You are the supervisor coordinating SQL generation agents.

Based on agent outputs, decide:
1. Workflow progression
2. Error recovery strategies
3. Final recommendations

Return JSON:
{
  "next_action": "proceed|retry|fallback|abort",
  "confidence": "high|medium|low",
  "message": "user-friendly explanation",
  "alternative_queries": []
}`,
    );
  }
}

export default SupervisorAgent;
