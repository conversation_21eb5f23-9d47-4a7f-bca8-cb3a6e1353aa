import LLMWrapper from './llm';

const llm = new LLMWrapper();

class Agent {
  name: string;
  systemPrompt: string;

  constructor(name: string, systemPrompt: string) {
    this.name = name;
    this.systemPrompt = systemPrompt;
  }

  async execute(input: string, context = {}) {
    const userPrompt = this.formatInput(input, context);
    const response = await llm.complete(this.systemPrompt, userPrompt);
    return this.parseResponse(response);
  }

  formatInput(input: string, context = {}) {
    return JSON.stringify({ input, context }, null, 2);
  }

  parseResponse(response: string | undefined) {
    if (!response) return { raw: 'No response provided' };
    try {
      if (response.includes('```json')) {
        const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          return JSON.parse(jsonMatch[1]);
        }
      } else if (response.includes('```sql')) {
        const sqlMatch = response.match(/```sql\s*([\s\S]*?)\s*```/);
        if (sqlMatch) {
          return sqlMatch[1];
        }
      }
      return JSON.parse(response);
    } catch (e) {
      return { raw: response };
    }
  }
}

export default Agent;
