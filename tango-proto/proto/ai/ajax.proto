syntax = 'proto3';

package ai;

option go_package = "./";

import "ai/shared.proto";

service AIAjaxService {
  rpc ReceiveAjaxEmail(EmailRequest) returns (GenericResponse);
}

message EmailMetadata {
  string fromEmailAddress = 1;
  string fromName = 2;
  string to = 3;
  string dkimSignature = 4;
  string messageId = 5;
  string receivedAt = 6;
  optional string inReplyTo = 7;
}

message EmailAttachment {
  string filename = 1;
  string contentType = 2;
  string base64Content = 3;
}

message EmailRequest {
  EmailMetadata metadata = 1;
  string subject = 2;
  string text = 3;
  string html = 4;
  repeated EmailAttachment attachments = 5;
}
